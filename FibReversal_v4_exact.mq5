//+------------------------------------------------------------------+
//|                                    Fibonacci Reversal EA v4     |
//|                    Exact replication of onurFibo.py Python script |
//+------------------------------------------------------------------+
#property version     "4.0"
#property description "Exact replication of Python fibonacci strategy"

#define FibReversalMagic 9765428

#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>

// Strategy parameters (matching Python script exactly)
input double InpMinYuzdeFark = 3.0;        // MIN_YUZDE_FARK = 0.03 (3%)
input double InpHardStopYuzde = 1.5;       // HARD_STOP_YUZDE = 0.015 (1.5%)
input double InpRiskPercent = 2.0;         // Risk percentage per trade
input double InpKaldirac = 15.0;           // KALDIRAC = 15 (leverage factor)

// Trading objects
CTrade m_trade;
CPositionInfo m_position;

// Python script state variables (exact replication)
enum ENUM_MODE
{
   MODE_YUKSELIS_FIBI_ARIYOR,    // 'Yükseliş Fibi Arıyor'
   MODE_DUSUS_FIBI_ARIYOR        // 'Düşüş Fibi Arıyor'
};

// Fibonacci class (exact replication of Python FibonacciRetracement)
class CFibonacciRetracement
{
public:
   string            type;           // 'Yükseliş' or 'Düşüş'
   double            tepe_fiyat;     // Peak price
   datetime          tepe_zaman;     // Peak time
   double            dip_fiyat;      // Trough price
   datetime          dip_zaman;      // Trough time
   double            level_236;      // 0.236 fibonacci level
   double            level_500;      // 0.5 fibonacci level
   
   void Init(double tepe, datetime tepe_time, double dip, datetime dip_time, string fib_type)
   {
      type = fib_type;
      tepe_fiyat = tepe;
      tepe_zaman = tepe_time;
      dip_fiyat = dip;
      dip_zaman = dip_time;
      
      double diff = MathAbs(tepe_fiyat - dip_fiyat);
      if(type == "Yükseliş")
      {
         level_236 = dip_fiyat + 0.236 * diff;
         level_500 = dip_fiyat + 0.5 * diff;
      }
      else // "Düşüş"
      {
         level_236 = tepe_fiyat - 0.236 * diff;
         level_500 = tepe_fiyat - 0.5 * diff;
      }
   }
};

// Global strategy state (exact replication of Python variables)
ENUM_MODE g_mode = MODE_YUKSELIS_FIBI_ARIYOR;
CFibonacciRetracement g_aktif_fib;
CFibonacciRetracement g_aday_fib;
bool g_aktif_fib_exists = false;
bool g_aday_fib_exists = false;

// Peak/trough tracking (exact replication)
double g_kilitli_tepe_fiyat = 0;
datetime g_kilitli_tepe_zaman = 0;
double g_kilitli_dip_fiyat = DBL_MAX;
datetime g_kilitli_dip_zaman = 0;

// Position tracking
ulong g_ticket = 0;
double g_entry_price = 0;
string g_position_type = "";

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // Initialize trade object
   m_trade.SetExpertMagicNumber(FibReversalMagic);
   m_trade.SetMarginMode();
   m_trade.SetTypeFillingBySymbol(Symbol());
   
   printf("=== Fibonacci Reversal EA v4 Initialized ===");
   printf("Parameters: MinYuzdeFark=%.1f%%, HardStop=%.1f%%, Risk=%.1f%%", 
          InpMinYuzdeFark, InpHardStopYuzde, InpRiskPercent);
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert tick function (process on new 4H bars only)             |
//+------------------------------------------------------------------+
void OnTick()
{
   static datetime lastBarTime = 0;
   datetime currentBarTime = iTime(NULL, PERIOD_H4, 0);
   
   if(currentBarTime != lastBarTime)
   {
      lastBarTime = currentBarTime;
      ProcessStrategy();
   }
}

//+------------------------------------------------------------------+
//| Main strategy processing (exact replication of Python logic)   |
//+------------------------------------------------------------------+
void ProcessStrategy()
{
   // Get current bar data (i = current index in Python loop)
   double close0 = iClose(NULL, PERIOD_H4, 2);  // close0 = df['Close'].iloc[i-2]
   double close1 = iClose(NULL, PERIOD_H4, 1);  // close1 = df['Close'].iloc[i-1]  
   double close2 = iClose(NULL, PERIOD_H4, 0);  // close2 = df['Close'].iloc[i]
   
   double high = iHigh(NULL, PERIOD_H4, 0);     // row['High']
   double low = iLow(NULL, PERIOD_H4, 0);       // row['Low']
   double close = iClose(NULL, PERIOD_H4, 0);   // row['Close']
   datetime current_time = iTime(NULL, PERIOD_H4, 0);
   
   // Python: if close0 > close1 > close2 and mode != 'Yükseliş Fibi Arıyor'
   if(close0 > close1 && close1 > close2 && g_mode != MODE_YUKSELIS_FIBI_ARIYOR)
   {
      g_mode = MODE_YUKSELIS_FIBI_ARIYOR;
      // Python: kilitli_tepe_fiyat = df['High'].iloc[i-2:i+1].max()
      g_kilitli_tepe_fiyat = MathMax(iHigh(NULL, PERIOD_H4, 2), MathMax(iHigh(NULL, PERIOD_H4, 1), high));
      g_kilitli_tepe_zaman = current_time; // Simplified - should find exact time of max
      printf("Mode switched to Yükseliş Fibi Arıyor - Peak locked at %.5f", g_kilitli_tepe_fiyat);
   }
   // Python: elif close0 < close1 < close2 and mode != 'Düşüş Fibi Arıyor'
   else if(close0 < close1 && close1 < close2 && g_mode != MODE_DUSUS_FIBI_ARIYOR)
   {
      g_mode = MODE_DUSUS_FIBI_ARIYOR;
      // Python: kilitli_dip_fiyat = df['Low'].iloc[i-2:i+1].min()
      g_kilitli_dip_fiyat = MathMin(iLow(NULL, PERIOD_H4, 2), MathMin(iLow(NULL, PERIOD_H4, 1), low));
      g_kilitli_dip_zaman = current_time; // Simplified - should find exact time of min
      printf("Mode switched to Düşüş Fibi Arıyor - Trough locked at %.5f", g_kilitli_dip_fiyat);
   }
   
   // Check active fibonacci position first (highest priority)
   if(g_aktif_fib_exists)
   {
      CheckActiveFibonacci(high, low, close, current_time);
      return; // Python: continue
   }
   
   // Check candidate fibonacci activation
   if(g_aday_fib_exists)
   {
      CheckCandidateFibonacci(close);
      return; // Python: continue  
   }
   
   // Look for new fibonacci candidates
   if(g_mode == MODE_YUKSELIS_FIBI_ARIYOR)
   {
      ProcessYukselisMode(high, low, current_time);
   }
   else if(g_mode == MODE_DUSUS_FIBI_ARIYOR)
   {
      ProcessDususMode(high, low, current_time);
   }
}

//+------------------------------------------------------------------+
//| Check active fibonacci for completion or stop                   |
//+------------------------------------------------------------------+
void CheckActiveFibonacci(double high, double low, double close, datetime current_time)
{
   bool success = false;
   bool fail = false;
   double kar_orani = 0;
   
   if(g_aktif_fib.type == "Yükseliş")
   {
      // Python: if row['Low'] < aktif_fib.level_236 * (1 - HARD_STOP_YUZDE)
      if(low < g_aktif_fib.level_236 * (1 - InpHardStopYuzde / 100.0))
      {
         fail = true;
         kar_orani = -InpHardStopYuzde;
      }
      // Python: elif row['High'] >= aktif_fib.level_500
      else if(high >= g_aktif_fib.level_500)
      {
         success = true;
         kar_orani = (g_aktif_fib.level_500 - g_aktif_fib.level_236) / g_aktif_fib.level_236 * 100.0;
      }
   }
   else // "Düşüş"
   {
      // Python: if row['High'] > aktif_fib.level_236 * (1 + HARD_STOP_YUZDE)
      if(high > g_aktif_fib.level_236 * (1 + InpHardStopYuzde / 100.0))
      {
         fail = true;
         kar_orani = -InpHardStopYuzde;
      }
      // Python: elif row['Low'] <= aktif_fib.level_500
      else if(low <= g_aktif_fib.level_500)
      {
         success = true;
         kar_orani = (g_aktif_fib.level_236 - g_aktif_fib.level_500) / g_aktif_fib.level_236 * 100.0;
      }
   }
   
   if(success || fail)
   {
      // Close position
      if(g_ticket > 0)
      {
         m_trade.PositionClose(g_ticket);
         printf("Position closed: %s - %s (%.2f%%)", 
                g_aktif_fib.type, 
                success ? "Fib Tamamlandı" : "Hard Stop",
                kar_orani);
      }
      
      // Reset active fibonacci (Python: aktif_fib = None)
      g_aktif_fib_exists = false;
      g_ticket = 0;
      g_entry_price = 0;
      g_position_type = "";
   }
}

//+------------------------------------------------------------------+
//| Check candidate fibonacci for activation                        |
//+------------------------------------------------------------------+
void CheckCandidateFibonacci(double close)
{
   if(g_aday_fib.type == "Yükseliş")
   {
      // Python: if row['Close'] < aday_fib.dip_fiyat or row['Close'] > aday_fib.tepe_fiyat: aday_fib = None
      if(close < g_aday_fib.dip_fiyat || close > g_aday_fib.tepe_fiyat)
      {
         g_aday_fib_exists = false;
         printf("Yükseliş candidate invalidated - Close %.5f outside range [%.5f - %.5f]",
                close, g_aday_fib.dip_fiyat, g_aday_fib.tepe_fiyat);
      }
      // Python: elif row['Close'] > aday_fib.level_236: aktif_fib = aday_fib; aday_fib = None
      else if(close > g_aday_fib.level_236)
      {
         g_aktif_fib = g_aday_fib;
         g_aktif_fib_exists = true;
         g_aday_fib_exists = false;

         // Open long position
         OpenPosition("Yükseliş");
         printf("YÜKSELIŞ ACTIVATION: Close %.5f > Fib 236 %.5f - opening long position",
                close, g_aktif_fib.level_236);
      }
   }
   else // "Düşüş"
   {
      // Python: if row['Close'] > aday_fib.tepe_fiyat or row['Close'] < aday_fib.dip_fiyat: aday_fib = None
      if(close > g_aday_fib.tepe_fiyat || close < g_aday_fib.dip_fiyat)
      {
         g_aday_fib_exists = false;
         printf("Düşüş candidate invalidated - Close %.5f outside range [%.5f - %.5f]",
                close, g_aday_fib.dip_fiyat, g_aday_fib.tepe_fiyat);
      }
      // Python: elif row['Close'] < aday_fib.level_236: aktif_fib = aday_fib; aday_fib = None
      else if(close < g_aday_fib.level_236)
      {
         g_aktif_fib = g_aday_fib;
         g_aktif_fib_exists = true;
         g_aday_fib_exists = false;

         // Open short position
         OpenPosition("Düşüş");
         printf("DÜŞÜŞ ACTIVATION: Close %.5f < Fib 236 %.5f - opening short position",
                close, g_aktif_fib.level_236);
      }
   }
}

//+------------------------------------------------------------------+
//| Process Yükseliş mode (looking for bullish fibonacci)          |
//+------------------------------------------------------------------+
void ProcessYukselisMode(double high, double low, datetime current_time)
{
   // Python: if row['High'] > kilitli_tepe_fiyat: kilitli_tepe_fiyat, kilitli_tepe_zaman = row['High'], current_time
   if(high > g_kilitli_tepe_fiyat)
   {
      g_kilitli_tepe_fiyat = high;
      g_kilitli_tepe_zaman = current_time;
      printf("Peak updated to %.5f (Yükseliş mode)", g_kilitli_tepe_fiyat);
   }
   // Python: elif kilitli_tepe_zaman: (only if peak is locked)
   else if(g_kilitli_tepe_zaman > 0)
   {
      // Python: fark = (kilitli_tepe_fiyat - row['Low']) / row['Low']
      double fark = (g_kilitli_tepe_fiyat - low) / low;

      // Python: if fark >= MIN_YUZDE_FARK:
      if(fark >= InpMinYuzdeFark / 100.0)
      {
         // Python: aday_fib = FibonacciRetracement(kilitli_tepe_fiyat, kilitli_tepe_zaman, row['Low'], current_time, 'Yükseliş')
         g_aday_fib.Init(g_kilitli_tepe_fiyat, g_kilitli_tepe_zaman, low, current_time, "Yükseliş");
         g_aday_fib_exists = true;

         printf("Yükseliş candidate created: Peak=%.5f, Trough=%.5f, Change=%.2f%%, Fib236=%.5f, Fib500=%.5f",
                g_aday_fib.tepe_fiyat, g_aday_fib.dip_fiyat, fark * 100.0,
                g_aday_fib.level_236, g_aday_fib.level_500);
      }
   }
}

//+------------------------------------------------------------------+
//| Process Düşüş mode (looking for bearish fibonacci)             |
//+------------------------------------------------------------------+
void ProcessDususMode(double high, double low, datetime current_time)
{
   // Python: if row['Low'] < kilitli_dip_fiyat: kilitli_dip_fiyat, kilitli_dip_zaman = row['Low'], current_time
   if(low < g_kilitli_dip_fiyat)
   {
      g_kilitli_dip_fiyat = low;
      g_kilitli_dip_zaman = current_time;
      printf("Trough updated to %.5f (Düşüş mode)", g_kilitli_dip_fiyat);
   }
   // Python: elif kilitli_dip_zaman: (only if trough is locked)
   else if(g_kilitli_dip_zaman > 0)
   {
      // Python: fark = (row['High'] - kilitli_dip_fiyat) / kilitli_dip_fiyat
      double fark = (high - g_kilitli_dip_fiyat) / g_kilitli_dip_fiyat;

      // Python: if fark >= MIN_YUZDE_FARK:
      if(fark >= InpMinYuzdeFark / 100.0)
      {
         // Python: aday_fib = FibonacciRetracement(row['High'], current_time, kilitli_dip_fiyat, kilitli_dip_zaman, 'Düşüş')
         g_aday_fib.Init(high, current_time, g_kilitli_dip_fiyat, g_kilitli_dip_zaman, "Düşüş");
         g_aday_fib_exists = true;

         printf("Düşüş candidate created: Peak=%.5f, Trough=%.5f, Change=%.2f%%, Fib236=%.5f, Fib500=%.5f",
                g_aday_fib.tepe_fiyat, g_aday_fib.dip_fiyat, fark * 100.0,
                g_aday_fib.level_236, g_aday_fib.level_500);
      }
   }
}

//+------------------------------------------------------------------+
//| Open position (long or short)                                   |
//+------------------------------------------------------------------+
void OpenPosition(string type)
{
   double lot_size = CalculateLotSize();
   double entry_price = 0;

   if(type == "Yükseliş")
   {
      entry_price = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
      if(m_trade.Buy(lot_size, Symbol()))
      {
         g_ticket = m_trade.ResultOrder();
         g_entry_price = entry_price;
         g_position_type = type;
         printf("Long position opened: Ticket=%d, Entry=%.5f, Lot=%.2f",
                (int)g_ticket, entry_price, lot_size);
      }
   }
   else // "Düşüş"
   {
      entry_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
      if(m_trade.Sell(lot_size, Symbol()))
      {
         g_ticket = m_trade.ResultOrder();
         g_entry_price = entry_price;
         g_position_type = type;
         printf("Short position opened: Ticket=%d, Entry=%.5f, Lot=%.2f",
                (int)g_ticket, entry_price, lot_size);
      }
   }
}

//+------------------------------------------------------------------+
//| Calculate lot size based on risk percentage                     |
//+------------------------------------------------------------------+
double CalculateLotSize()
{
   double balance = AccountInfoDouble(ACCOUNT_BALANCE);
   double risk_amount = balance * InpRiskPercent / 100.0;

   // Simple lot calculation - can be improved
   double lot_size = risk_amount / 1000.0; // Simplified calculation

   // Apply minimum and maximum lot size constraints
   double min_lot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
   double max_lot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX);
   double lot_step = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_STEP);

   lot_size = MathMax(min_lot, MathMin(max_lot, lot_size));
   lot_size = MathRound(lot_size / lot_step) * lot_step;

   return lot_size;
}
